"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/weather/forecast-list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square-text.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecastList = (param)=>{\n    let { logBookEntryID, refreshList = false, onClick, offline = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(true);\n    const [forecasts, setForecasts] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [openCommentDialog, setOpenCommentDialog] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [currentForecast, setCurrentForecast] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const [readWeatherForecasts, { loading: readWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.ReadWeatherForecasts, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readWeatherForecasts;\n            const forecasts = response === null || response === void 0 ? void 0 : (_response_readWeatherForecasts = response.readWeatherForecasts) === null || _response_readWeatherForecasts === void 0 ? void 0 : _response_readWeatherForecasts.nodes;\n            setForecasts(forecasts);\n        },\n        onError: (error)=>{\n            console.error(\"ReadWeatherForecasts error\", error);\n        }\n    });\n    const [updateWeatherForecast] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setOpenCommentDialog(false);\n            setIsSaving(false);\n            setHasError(false);\n            loadForecasts() // Refresh the list\n            ;\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast error\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    });\n    const loadForecasts = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(async ()=>{\n        if (offline) {\n            const forecasts = await forecastModel.getByLogBookEntryID(logBookEntryID);\n            setForecasts(forecasts);\n        } else {\n            await readWeatherForecasts({\n                variables: {\n                    filter: {\n                        logBookEntryID: {\n                            eq: logBookEntryID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        offline,\n        logBookEntryID,\n        forecastModel,\n        readWeatherForecasts\n    ]);\n    const handleOpenCommentDialog = (forecast)=>{\n        setCurrentForecast(forecast);\n        setCurrentComment(forecast.comment || \"\");\n        setOpenCommentDialog(true);\n        setHasError(false);\n    };\n    const handleSaveComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: currentComment\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: currentComment\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const handleDeleteComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: \"\"\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: \"\"\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error deleting comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const formatDay = (day)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(day).format(\"YYYY-MM-DD\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isLoading || refreshList) {\n            loadForecasts();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        refreshList,\n        loadForecasts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WeatherForecastListSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 184,\n                columnNumber: 45\n            }, undefined),\n            !readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: forecasts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        headings: [\n                            \"Forecast\",\n                            \"Wind\",\n                            \"Cloud\",\n                            \"Swell\",\n                            \"Comment\"\n                        ],\n                        children: forecasts.map((forecast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"group border-b  hover: \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"cursor-pointer\",\n                                            onClick: ()=>{\n                                                onClick(forecast);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-light\",\n                                                        children: dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(forecast.day, \" \").concat(forecast.time)).format(\"DD MMMM, HH:mm\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"uppercase text-light-blue-vivid-300\",\n                                                        children: +forecast.geoLocationID > 0 ? forecast.geoLocation.title : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: forecast.windDirection,\n                                            speed: forecast.windSpeed,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            visibilityValue: forecast.visibility,\n                                            precipitationValue: forecast.precipitation,\n                                            cloudCoverValue: forecast.cloudCover,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            value: forecast.swell,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Popover, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.PopoverTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        iconOnly: true,\n                                                        title: forecast.comment ? \"Edit comment\" : \"Add comment\",\n                                                        className: \"group\",\n                                                        iconLeft: forecast.comment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-curious-blue-400\"),\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 65\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-neutral-400 group-hover:text-neutral-400/50\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\"),\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 65\n                                                        }, void 0),\n                                                        onClick: ()=>handleOpenCommentDialog(forecast)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.PopoverContent, {\n                                                    children: forecast.comment\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, forecast.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 37\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 186,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__.AlertDialogNew, {\n                openDialog: openCommentDialog,\n                setOpenDialog: setOpenCommentDialog,\n                title: currentComment ? \"Edit comment\" : \"Add comment\",\n                handleCreate: handleSaveComment,\n                handleDestructiveAction: currentComment ? handleDeleteComment : undefined,\n                showDestructiveAction: !!currentComment,\n                actionText: isSaving ? \"Saving...\" : \"Save\",\n                destructiveActionText: \"Delete\",\n                destructiveLoading: isSaving,\n                cancelText: \"Cancel\",\n                size: \"lg\",\n                loading: isSaving,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-destructive mb-2 text-sm\",\n                            children: [\n                                \"Error \",\n                                currentComment ? \"updating\" : \"saving\",\n                                \" \",\n                                \"comment. Please try again.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                            id: \"forecast-comment\",\n                            disabled: isSaving,\n                            rows: 4,\n                            placeholder: \"Comment\",\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"max-h-[60svh]\", {\n                                \"border-destructive\": hasError\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 21\n                        }, undefined),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Saving...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 313,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 183,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecastList, \"2xbn5GwkfoDvXkmLYY/Eialx23A=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation\n    ];\n});\n_c = WeatherForecastList;\nconst WeatherForecastListSkeleton = ()=>{\n    const numRows = 3 // number of rows to render\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        headings: [\n            \"Time:firstHead\",\n            \"Location\"\n        ],\n        children: Array.from({\n            length: numRows\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"group border-b  hover: \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4 text-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 364,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 362,\n        columnNumber: 9\n    }, undefined);\n};\n_c1 = WeatherForecastListSkeleton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecastList);\nvar _c, _c1;\n$RefreshReg$(_c, \"WeatherForecastList\");\n$RefreshReg$(_c1, \"WeatherForecastListSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\n"));

/***/ })

});