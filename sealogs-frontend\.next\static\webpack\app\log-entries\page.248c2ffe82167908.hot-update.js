"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/weather/forecast-list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square-text.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecastList = (param)=>{\n    let { logBookEntryID, refreshList = false, onClick, offline = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(true);\n    const [forecasts, setForecasts] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [openCommentDialog, setOpenCommentDialog] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [currentForecast, setCurrentForecast] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const [readWeatherForecasts, { loading: readWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.ReadWeatherForecasts, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readWeatherForecasts;\n            const forecasts = response === null || response === void 0 ? void 0 : (_response_readWeatherForecasts = response.readWeatherForecasts) === null || _response_readWeatherForecasts === void 0 ? void 0 : _response_readWeatherForecasts.nodes;\n            setForecasts(forecasts);\n        },\n        onError: (error)=>{\n            console.error(\"ReadWeatherForecasts error\", error);\n        }\n    });\n    const [updateWeatherForecast] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setOpenCommentDialog(false);\n            setIsSaving(false);\n            setHasError(false);\n            loadForecasts() // Refresh the list\n            ;\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast error\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    });\n    const loadForecasts = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(async ()=>{\n        if (offline) {\n            const forecasts = await forecastModel.getByLogBookEntryID(logBookEntryID);\n            setForecasts(forecasts);\n        } else {\n            await readWeatherForecasts({\n                variables: {\n                    filter: {\n                        logBookEntryID: {\n                            eq: logBookEntryID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        offline,\n        logBookEntryID,\n        forecastModel,\n        readWeatherForecasts\n    ]);\n    const handleOpenCommentDialog = (forecast)=>{\n        setCurrentForecast(forecast);\n        setCurrentComment(forecast.comment || \"\");\n        setOpenCommentDialog(true);\n        setHasError(false);\n    };\n    const handleSaveComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: currentComment\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: currentComment\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const handleDeleteComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: \"\"\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: \"\"\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error deleting comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const formatDay = (day)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(day).format(\"YYYY-MM-DD\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isLoading || refreshList) {\n            loadForecasts();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        refreshList,\n        loadForecasts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WeatherForecastListSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 191,\n                columnNumber: 45\n            }, undefined),\n            !readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: forecasts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        headings: [\n                            \"Forecast\",\n                            \"Wind\",\n                            \"Cloud\",\n                            \"Swell\",\n                            \"Comment\"\n                        ],\n                        children: forecasts.map((forecast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"group border-b  hover: \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"cursor-pointer\",\n                                            onClick: ()=>{\n                                                onClick(forecast);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-light\",\n                                                        children: dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(forecast.day, \" \").concat(forecast.time)).format(\"DD MMMM, HH:mm\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"uppercase text-light-blue-vivid-300\",\n                                                        children: +forecast.geoLocationID > 0 ? forecast.geoLocation.title : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: forecast.windDirection,\n                                            speed: forecast.windSpeed,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            visibilityValue: forecast.visibility,\n                                            precipitationValue: forecast.precipitation,\n                                            cloudCoverValue: forecast.cloudCover,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            value: forecast.swell,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        iconOnly: true,\n                                                        title: forecast.comment ? \"Edit comment\" : \"Add comment\",\n                                                        className: \"group\",\n                                                        iconLeft: forecast.comment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-curious-blue-400\"),\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 65\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-neutral-400 group-hover:text-neutral-400/50\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\"),\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 65\n                                                        }, void 0),\n                                                        onClick: ()=>handleOpenCommentDialog(forecast)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                forecast.comment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                                    children: forecast.comment\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 53\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, forecast.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 37\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 193,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__.AlertDialogNew, {\n                openDialog: openCommentDialog,\n                setOpenDialog: setOpenCommentDialog,\n                title: currentComment ? \"Edit comment\" : \"Add comment\",\n                handleCreate: handleSaveComment,\n                handleDestructiveAction: currentComment ? handleDeleteComment : undefined,\n                showDestructiveAction: !!currentComment,\n                actionText: isSaving ? \"Saving...\" : \"Save\",\n                destructiveActionText: \"Delete\",\n                destructiveLoading: isSaving,\n                cancelText: \"Cancel\",\n                size: \"lg\",\n                loading: isSaving,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-destructive mb-2 text-sm\",\n                            children: [\n                                \"Error \",\n                                currentComment ? \"updating\" : \"saving\",\n                                \" \",\n                                \"comment. Please try again.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                            id: \"forecast-comment\",\n                            disabled: isSaving,\n                            rows: 4,\n                            placeholder: \"Comment\",\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"max-h-[60svh]\", {\n                                \"border-destructive\": hasError\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 21\n                        }, undefined),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Saving...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 322,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecastList, \"2xbn5GwkfoDvXkmLYY/Eialx23A=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation\n    ];\n});\n_c = WeatherForecastList;\nconst WeatherForecastListSkeleton = ()=>{\n    const numRows = 3 // number of rows to render\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        headings: [\n            \"Time:firstHead\",\n            \"Location\"\n        ],\n        children: Array.from({\n            length: numRows\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"group border-b  hover: \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4 text-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 373,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 371,\n        columnNumber: 9\n    }, undefined);\n};\n_c1 = WeatherForecastListSkeleton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecastList);\nvar _c, _c1;\n$RefreshReg$(_c, \"WeatherForecastList\");\n$RefreshReg$(_c1, \"WeatherForecastListSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\n"));

/***/ })

});