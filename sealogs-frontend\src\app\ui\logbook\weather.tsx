import { useEffect, useState } from 'react'
import WeatherForecast from '../weather/forecast'
import Tide from '../weather/tide'
import ResponsiveTab from '@/components/ui/responsive-tab'
import { TabItem } from '@/components/ui/custom-tab'
const LogBookWeather = ({
    logBookConfig,
    logbook,
    offline = false,
}: {
    logBookConfig: any
    logbook: any
    offline?: boolean
}) => {
    const [locked, setLocked] = useState(logbook.locked)

    useEffect(() => {
        logbook.state === 'Locked' ? setLocked(true) : setLocked(false)
    }, [logbook])

    const displayField = (fieldName: string) => {
        const element =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'Weather_LogBookComponent',
            )
        if (
            element?.length > 0 &&
            element[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    // Define tab items for the ResponsiveTabsAccordion
    const getTabItems = (): TabItem[] => {
        const tabs: TabItem[] = [
            {
                id: 'forecast_observation',
                value: 'forecast_observation',
                label: 'Forecasts & observations',
                component: (
                    <WeatherForecast
                        offline={offline}
                        logBookEntryID={logbook.id}
                        locked={locked}
                    />
                ),
            },
        ]

        // Add Tides tab if it should be displayed
        if (displayField('Tides')) {
            tabs.push({
                id: 'tide',
                value: 'tide',
                label: 'Tides',
                component: <Tide logBookEntryID={logbook.id} locked={locked} />,
            })
        }

        return tabs
    }

    return <ResponsiveTab tabs={getTabItems()} queryParam="weatherTab" />
}

export default LogBookWeather
