"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/weather/forecast.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _logbook_components_location__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/weatherHelper */ \"(app-pages-browser)/./src/app/helpers/weatherHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _forecast_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forecast-list */ \"(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./widgets/barometer-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/barometer-widget.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecast = (param)=>{\n    let { logBookEntryID, offline = false, locked } = param;\n    _s();\n    const [isWriteModeForecast, setIsWriteModeForecast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isManualEntry, setIsManualEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forecast, setForecast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Current location state for the location field\n    const [currentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const [selectedCoordinates, setSelectedCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const isOnline = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline)();\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const [isStormGlassLoading, setIsStormGlassLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshList, setRefreshList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [geoLocations, setGeoLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    const getTimeNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\");\n    };\n    const [getGeoLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readGeoLocations.nodes;\n            if (data) {\n                setGeoLocations(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryGeoLocations error\", error);\n        }\n    });\n    const getDayNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\");\n    };\n    const initForecast = ()=>{\n        setForecast({\n            id: 0,\n            time: getTimeNow(),\n            day: getDayNow(),\n            geoLocationID: 0,\n            lat: 0,\n            long: 0,\n            logBookEntryID: logBookEntryID\n        });\n    };\n    const createForecast = ()=>{\n        initForecast();\n        setIsWriteModeForecast(true);\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setForecast({\n            ...forecast,\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        });\n        setSelectedCoordinates({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setForecast({\n                ...forecast,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, use them directly\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setSelectedCoordinates({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            } else {\n                // Otherwise find the location in geoLocations by ID\n                geoLocations.find((item)=>{\n                    if (item.id == +value.value) {\n                        setSelectedCoordinates({\n                            latitude: item.lat,\n                            longitude: item.long\n                        });\n                        return true;\n                    }\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setForecast({\n                ...forecast,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update selected coordinates\n            setSelectedCoordinates({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    };\n    const processStormGlassData = (data)=>{\n        const { windSpeed, windDirection, swellHeight, visibility, precipitation, pressure, cloudCover } = data.hours[0];\n        const windSpeedInKnots = (windSpeed ? (windSpeed.noaa || windSpeed.sg || 0) / 0.51444 : 0).toFixed(0) // Convert m/s to knot. One knot is equal to approximately 0.51444 meters per second (m/s).\n        ;\n        const compassWindDirection = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getWindDirection)(windDirection ? windDirection.noaa || windDirection.sg || 0 : 0) // convert degrees to compass direction\n        ;\n        const swellValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getSwellHeightRange)(swellHeight ? swellHeight.noaa || swellHeight.sg || 0 : 0);\n        const visibilityValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getVisibility)(visibility ? visibility.noaa || visibility.sg || 0 : 0);\n        const precipitationValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getPrecipitation)(precipitation ? precipitation.noaa || precipitation.sg || 0 : 0);\n        const pressureValue = pressure ? pressure.noaa || pressure.sg || 0 : 0;\n        const cloudCoverValue = (cloudCover ? cloudCover.noaa || cloudCover.sg || 0 : 0).toFixed(0);\n        setForecast({\n            ...forecast,\n            windSpeed: +windSpeedInKnots,\n            windDirection: compassWindDirection,\n            swell: swellValue,\n            visibility: visibilityValue,\n            precipitation: precipitationValue,\n            pressure: +pressureValue,\n            cloudCover: +cloudCoverValue\n        });\n        setIsStormGlassLoading(false);\n    };\n    const isStormGlassButtonEnabled = ()=>{\n        let isStormGlassButtonEnabled = false;\n        if (+forecast.geoLocationID > 0) {\n            isStormGlassButtonEnabled = true;\n        } else if (!isNaN(+forecast.lat) || !isNaN(+forecast.long)) {\n            isStormGlassButtonEnabled = true;\n        }\n        if (!isOnline) {\n            isStormGlassButtonEnabled = false;\n        }\n        return isStormGlassButtonEnabled;\n    };\n    const getStormGlassData = ()=>{\n        setIsManualEntry(false);\n        if (forecast.geoLocationID > 0) {\n            toast({\n                title: \"Loading\",\n                description: \"Retrieving forecast...\"\n            });\n            setIsStormGlassLoading(true);\n            const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n            let startDate = new Date(dateString);\n            let endDate = startDate;\n            var headers = {\n                \"Cache-Control\": \"no-cache\",\n                Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                \"Access-Control-Allow-Credentials\": \"true\"\n            };\n            var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n            const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n            let request = fetch(url, {\n                method: \"GET\",\n                headers\n            });\n            request.then((response)=>response.json()).then((jsonData)=>{\n                toast({\n                    title: \"Success\",\n                    description: \"Forecast retrieved successfully\",\n                    variant: \"default\"\n                });\n                processStormGlassData(jsonData);\n            }).catch((error)=>{\n                setIsStormGlassLoading(false);\n                toast({\n                    title: \"Error\",\n                    description: \"There was a problem retrieving the forecast. Please try again later.\",\n                    variant: \"destructive\"\n                });\n                console.error(\"Catch error:\", error);\n            });\n            return request;\n        } else {\n            if (\"geolocation\" in navigator) {\n                toast({\n                    title: \"Loading\",\n                    description: \"Retrieving forecast...\"\n                });\n                setIsStormGlassLoading(true);\n                return new Promise((resolve, reject)=>{\n                    return navigator.geolocation.getCurrentPosition(()=>{\n                        const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n                        let startDate = new Date(dateString);\n                        let endDate = startDate;\n                        var headers = {\n                            \"Cache-Control\": \"no-cache\",\n                            Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                            \"Access-Control-Allow-Credentials\": \"true\"\n                        };\n                        var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n                        const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n                        let request = fetch(url, {\n                            method: \"GET\",\n                            headers\n                        });\n                        request.then((response)=>response.json()).then((jsonData)=>{\n                            toast({\n                                title: \"Success\",\n                                description: \"Forecast retrieved successfully\",\n                                variant: \"default\"\n                            });\n                            processStormGlassData(jsonData);\n                            resolve(jsonData);\n                        }).catch((error)=>{\n                            setIsStormGlassLoading(false);\n                            reject(error);\n                            toast({\n                                title: \"Error\",\n                                description: \"There was a problem retrieving the forecast. Please try again later.\",\n                                variant: \"destructive\"\n                            });\n                            console.error(\"Catch error:\", error);\n                        });\n                        return request;\n                    }, (error)=>{\n                        setIsStormGlassLoading(false);\n                        reject(error);\n                        toast({\n                            title: \"Error\",\n                            description: \"There was a problem retrieving the forecast. Please try again later.\",\n                            variant: \"destructive\"\n                        });\n                        console.error(\"Geolocation error\", error);\n                    });\n                });\n            } else {\n                setIsStormGlassLoading(false);\n                console.error(\"Geolocation is not supported by your browser\");\n                toast({\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const handleOnChangePressure = (value)=>{\n        const pressure = Array.isArray(value) ? value[0] : value;\n        setForecast({\n            ...forecast,\n            pressure: pressure\n        });\n    };\n    const handleOnChangeSeaSwell = (item)=>{\n        if (item) {\n            setForecast({\n                ...forecast,\n                swell: item.value\n            });\n        }\n    };\n    const handleWindChange = (values)=>{\n        // Update both wind speed and direction in the forecast state\n        setForecast({\n            ...forecast,\n            windSpeed: values.speed,\n            windDirection: values.direction.value\n        });\n    };\n    const handleSetComment = lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default()((item)=>{\n        setForecast({\n            ...forecast,\n            comment: item\n        });\n    }, 600);\n    const [createWeatherForecast, { loading: createWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CreateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"CreateWeatherForecast Error\", error);\n        }\n    });\n    const [updateWeatherForecast, { loading: updateWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast Error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        if (+forecast.id === 0) {\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await createWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        } else {\n            if (forecast.geoLocation) delete forecast.geoLocation;\n            if (forecast.__typename) delete forecast.__typename;\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day).format(\"YYYY-MM-DD\")\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await updateWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleCancel = ()=>{\n        initForecast();\n        setIsWriteModeForecast(false);\n    };\n    const handleForecastClick = (forecast)=>{\n        if (locked) {\n            return;\n        }\n        setIsManualEntry(false);\n        const newForecast = {\n            ...forecast,\n            time: formatTime(forecast.time),\n            day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day.toString()).format(\"YYYY-MM-DD\")\n        };\n        setForecast(newForecast);\n        setIsWriteModeForecast(true);\n    };\n    const [deleteWeatherForecasts, { loading: deleteWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DeleteWeatherForecasts, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"DeleteWeatherForecasts Error\", error);\n        }\n    });\n    const handleDeleteForecast = async ()=>{\n        if (offline) {\n            forecastModel.delete(forecast);\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        } else {\n            await deleteWeatherForecasts({\n                variables: {\n                    ids: [\n                        forecast.id\n                    ]\n                }\n            });\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const handleOnChangeCloudWidget = (item)=>{\n        setForecast({\n            ...forecast,\n            visibility: item.visibility.value,\n            precipitation: item.precipitation.value,\n            cloudCover: item.cloudCover\n        });\n    };\n    // Format time string for display\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logBookEntryID > 0) {\n            setForecast({\n                ...forecast,\n                logBookEntryID: logBookEntryID\n            });\n        }\n        getGeoLocations();\n    }, [\n        logBookEntryID\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_18__.Card, {\n        children: [\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H2, {\n                                children: \"Weather forecast\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"You can start by retrieving a weather forecast up to 7-days into the future. SeaLogs currently using the Stormglass API with more forecasting services coming soon.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"After retrieving a forecast you can add your own observations. We use this data to compare the accuracy of forecasts plus share weather observations with our community of users.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                            disabled: locked,\n                            onClick: ()=>createForecast(),\n                            children: \"Add another forecast\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            id: \"forecast-date\",\n                                            name: \"forecast-date\",\n                                            label: \"Date and time of forecast required\",\n                                            value: forecast.day && forecast.time ? new Date(\"\".concat(forecast.day, \"T\").concat(forecast.time)) : undefined,\n                                            mode: \"single\",\n                                            type: \"datetime\" // Keep datetime to include time picker\n                                            ,\n                                            onChange: (date)=>{\n                                                if (date) {\n                                                    const newDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date);\n                                                    setForecast({\n                                                        ...forecast,\n                                                        day: newDate.format(\"YYYY-MM-DD\"),\n                                                        time: newDate.format(\"HH:mm:00\")\n                                                    });\n                                                }\n                                            },\n                                            dateFormat: \"dd MMM,\",\n                                            timeFormat: \"HH:mm\" // Explicitly set time format\n                                            ,\n                                            placeholder: \"Time\",\n                                            closeOnSelect: false,\n                                            clearable: true,\n                                            icon: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                            className: \"w-full\",\n                                            includeTime: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            label: \"Location for forecast\",\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_components_location__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    offline: offline,\n                                                    setCurrentLocation: handleSetCurrentLocation,\n                                                    handleLocationChange: (e)=>{\n                                                        handleLocationChange(e);\n                                                    },\n                                                    currentEvent: {\n                                                        geoLocationID: forecast.geoLocationID,\n                                                        lat: forecast.lat,\n                                                        long: forecast.long\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    isWriteModeForecast && isStormGlassButtonEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            onClick: ()=>getStormGlassData(),\n                                            disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                            className: \"w-full\",\n                                            children: isStormGlassLoading ? \"Retrieving forecast...\" : \"Retrieve forecast (API)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: \"secondary\",\n                                            onClick: ()=>setIsManualEntry(true),\n                                            className: \"w-full\",\n                                            children: \"OR enter a manual forecast (observation)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: isManualEntry ? \"flex flex-col gap-4\" : \"grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        speed: forecast.windSpeed,\n                                        direction: forecast.windDirection,\n                                        onChange: handleWindChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        visibilityValue: forecast.visibility,\n                                        precipitationValue: forecast.precipitation,\n                                        cloudCoverValue: forecast.cloudCover,\n                                        onChange: handleOnChangeCloudWidget\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        value: forecast.swell,\n                                        onChange: handleOnChangeSeaSwell\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        value: forecast.pressure,\n                                        editMode: isManualEntry,\n                                        onChange: handleOnChangePressure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 25\n                            }, undefined),\n                            !isManualEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-[10px]\",\n                                children: \"Forecast provided by Stormglass API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    label: \"Your comments\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__.Textarea, {\n                                        id: \"forecast-comment\",\n                                        rows: 4,\n                                        placeholder: \"Comments ...\",\n                                        defaultValue: forecast.comment || \"\",\n                                        onChange: (e)=>handleSetComment(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"flex justify-end mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 md:flex-row md:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                    variant: \"back\",\n                                    onClick: handleCancel,\n                                    disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 29\n                                }, undefined),\n                                +forecast.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                    variant: \"destructive\",\n                                    iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                    onClick: ()=>{\n                                        setDeleteDialogOpen(true);\n                                    },\n                                    disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                    iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                    onClick: handleSave,\n                                    disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                    children: \"\".concat(+forecast.id === 0 ? \"Save\" : \"Update\", \" forecast\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 544,\n                columnNumber: 17\n            }, undefined),\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forecast_list__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    logBookEntryID: logBookEntryID,\n                    refreshList: refreshList,\n                    onClick: handleForecastClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                    lineNumber: 741,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 740,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: deleteDialogOpen,\n                setOpenDialog: setDeleteDialogOpen,\n                handleCreate: handleDeleteForecast,\n                title: \"Delete Forecast Data\",\n                description: \"Are you sure you want to delete the forecast data for \".concat(forecast.time ? formatTime(forecast.time) : \"\", \" / \").concat(forecast.day ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__.formatDate)(forecast.day) : \"\", \"?\"),\n                variant: \"danger\",\n                actionText: \"Delete\",\n                cancelText: \"Cancel\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 750,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n        lineNumber: 516,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecast, \"SLM0CI70UQRIE739lbwnzi7zfIQ=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c = WeatherForecast;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecast);\nvar _c;\n$RefreshReg$(_c, \"WeatherForecast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast.tsx\n"));

/***/ })

});