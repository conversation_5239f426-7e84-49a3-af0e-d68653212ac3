"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/weather/forecast-list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square-text.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecastList = (param)=>{\n    let { logBookEntryID, refreshList = false, onClick, offline = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(true);\n    const [forecasts, setForecasts] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [openCommentDialog, setOpenCommentDialog] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [currentForecast, setCurrentForecast] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const [readWeatherForecasts, { loading: readWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.ReadWeatherForecasts, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readWeatherForecasts;\n            const forecasts = response === null || response === void 0 ? void 0 : (_response_readWeatherForecasts = response.readWeatherForecasts) === null || _response_readWeatherForecasts === void 0 ? void 0 : _response_readWeatherForecasts.nodes;\n            setForecasts(forecasts);\n        },\n        onError: (error)=>{\n            console.error(\"ReadWeatherForecasts error\", error);\n        }\n    });\n    const [updateWeatherForecast] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setOpenCommentDialog(false);\n            setIsSaving(false);\n            setHasError(false);\n            loadForecasts() // Refresh the list\n            ;\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast error\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    });\n    const loadForecasts = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(async ()=>{\n        if (offline) {\n            const forecasts = await forecastModel.getByLogBookEntryID(logBookEntryID);\n            setForecasts(forecasts);\n        } else {\n            await readWeatherForecasts({\n                variables: {\n                    filter: {\n                        logBookEntryID: {\n                            eq: logBookEntryID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        offline,\n        logBookEntryID,\n        forecastModel,\n        readWeatherForecasts\n    ]);\n    const handleOpenCommentDialog = (forecast)=>{\n        setCurrentForecast(forecast);\n        setCurrentComment(forecast.comment || \"\");\n        setOpenCommentDialog(true);\n        setHasError(false);\n    };\n    const handleSaveComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: currentComment\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: currentComment\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const handleDeleteComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: \"\"\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: \"\"\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error deleting comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const formatDay = (day)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(day).format(\"YYYY-MM-DD\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isLoading || refreshList) {\n            loadForecasts();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        refreshList,\n        loadForecasts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WeatherForecastListSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 184,\n                columnNumber: 45\n            }, undefined),\n            !readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: forecasts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        headings: [\n                            \"Forecast\",\n                            \"Wind\",\n                            \"Cloud\",\n                            \"Swell\",\n                            \"Comment\"\n                        ],\n                        children: forecasts.map((forecast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"group border-b  hover: \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"cursor-pointer\",\n                                            onClick: ()=>{\n                                                onClick(forecast);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-light\",\n                                                        children: dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(forecast.day, \" \").concat(forecast.time)).format(\"DD MMMM, HH:mm\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"uppercase text-light-blue-vivid-300\",\n                                                        children: +forecast.geoLocationID > 0 ? forecast.geoLocation.title : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: forecast.windDirection,\n                                            speed: forecast.windSpeed,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            visibilityValue: forecast.visibility,\n                                            precipitationValue: forecast.precipitation,\n                                            cloudCoverValue: forecast.cloudCover,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            value: forecast.swell,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Popover, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.PopoverTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        iconOnly: true,\n                                                        title: forecast.comment ? \"Edit comment\" : \"Add comment\",\n                                                        className: \"group\",\n                                                        iconLeft: forecast.comment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-curious-blue-400\"),\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 65\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-neutral-400 group-hover:text-neutral-400/50\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\"),\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 65\n                                                        }, void 0),\n                                                        onClick: ()=>handleOpenCommentDialog(forecast)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.PopoverContent, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, forecast.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 37\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 186,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__.AlertDialogNew, {\n                openDialog: openCommentDialog,\n                setOpenDialog: setOpenCommentDialog,\n                title: currentComment ? \"Edit comment\" : \"Add comment\",\n                handleCreate: handleSaveComment,\n                handleDestructiveAction: currentComment ? handleDeleteComment : undefined,\n                showDestructiveAction: !!currentComment,\n                actionText: isSaving ? \"Saving...\" : \"Save\",\n                destructiveActionText: \"Delete\",\n                destructiveLoading: isSaving,\n                cancelText: \"Cancel\",\n                size: \"lg\",\n                loading: isSaving,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-destructive mb-2 text-sm\",\n                            children: [\n                                \"Error \",\n                                currentComment ? \"updating\" : \"saving\",\n                                \" \",\n                                \"comment. Please try again.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                            id: \"forecast-comment\",\n                            disabled: isSaving,\n                            rows: 4,\n                            placeholder: \"Comment\",\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"max-h-[60svh]\", {\n                                \"border-destructive\": hasError\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 21\n                        }, undefined),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Saving...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 313,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 183,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecastList, \"2xbn5GwkfoDvXkmLYY/Eialx23A=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation\n    ];\n});\n_c = WeatherForecastList;\nconst WeatherForecastListSkeleton = ()=>{\n    const numRows = 3 // number of rows to render\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        headings: [\n            \"Time:firstHead\",\n            \"Location\"\n        ],\n        children: Array.from({\n            length: numRows\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"group border-b  hover: \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4 text-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 364,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 362,\n        columnNumber: 9\n    }, undefined);\n};\n_c1 = WeatherForecastListSkeleton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecastList);\nvar _c, _c1;\n$RefreshReg$(_c, \"WeatherForecastList\");\n$RefreshReg$(_c1, \"WeatherForecastListSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\n"));

/***/ })

});