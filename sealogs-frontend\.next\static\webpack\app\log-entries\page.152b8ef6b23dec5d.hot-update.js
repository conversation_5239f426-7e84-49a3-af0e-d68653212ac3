"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/weather/forecast-list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square-text.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecastList = (param)=>{\n    let { logBookEntryID, refreshList = false, onClick, offline = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(true);\n    const [forecasts, setForecasts] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [openCommentDialog, setOpenCommentDialog] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [currentForecast, setCurrentForecast] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const [readWeatherForecasts, { loading: readWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.ReadWeatherForecasts, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readWeatherForecasts;\n            const forecasts = response === null || response === void 0 ? void 0 : (_response_readWeatherForecasts = response.readWeatherForecasts) === null || _response_readWeatherForecasts === void 0 ? void 0 : _response_readWeatherForecasts.nodes;\n            setForecasts(forecasts);\n        },\n        onError: (error)=>{\n            console.error(\"ReadWeatherForecasts error\", error);\n        }\n    });\n    const [updateWeatherForecast] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setOpenCommentDialog(false);\n            setIsSaving(false);\n            setHasError(false);\n            loadForecasts() // Refresh the list\n            ;\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast error\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    });\n    const loadForecasts = async ()=>{\n        if (offline) {\n            const forecasts = await forecastModel.getByLogBookEntryID(logBookEntryID);\n            setForecasts(forecasts);\n        } else {\n            await readWeatherForecasts({\n                variables: {\n                    filter: {\n                        logBookEntryID: {\n                            eq: logBookEntryID\n                        }\n                    }\n                }\n            });\n        }\n    };\n    const handleOpenCommentDialog = (forecast)=>{\n        setCurrentForecast(forecast);\n        setCurrentComment(forecast.comment || \"\");\n        setOpenCommentDialog(true);\n        setHasError(false);\n    };\n    const handleSaveComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: currentComment\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: currentComment\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const handleDeleteComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: \"\"\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: \"\"\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error deleting comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const formatDay = (day)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(day).format(\"YYYY-MM-DD\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isLoading || refreshList) {\n            loadForecasts();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        refreshList,\n        loadForecasts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WeatherForecastListSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 183,\n                columnNumber: 45\n            }, undefined),\n            !readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: forecasts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        headings: [\n                            \"Forecast\",\n                            \"Wind\",\n                            \"Cloud\",\n                            \"Swell\",\n                            \"Comment\"\n                        ],\n                        children: forecasts.map((forecast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"group border-b  hover: \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"cursor-pointer\",\n                                            onClick: ()=>{\n                                                onClick(forecast);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-light\",\n                                                        children: dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(forecast.day, \" \").concat(forecast.time)).format(\"DD MMMM, HH:mm\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"uppercase text-light-blue-vivid-300\",\n                                                        children: +forecast.geoLocationID > 0 ? forecast.geoLocation.title : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: forecast.windDirection,\n                                            speed: forecast.windSpeed,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            visibilityValue: forecast.visibility,\n                                            precipitationValue: forecast.precipitation,\n                                            cloudCoverValue: forecast.cloudCover,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            value: forecast.swell,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            iconOnly: true,\n                                            title: forecast.comment ? \"Edit comment\" : \"Add comment\",\n                                            className: \"group\",\n                                            iconLeft: forecast.comment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-curious-blue-400\"),\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 57\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-neutral-400 group-hover:text-neutral-400/50\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\"),\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 57\n                                            }, void 0),\n                                            onClick: ()=>handleOpenCommentDialog(forecast)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, forecast.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 37\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 185,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__.AlertDialogNew, {\n                openDialog: openCommentDialog,\n                setOpenDialog: setOpenCommentDialog,\n                title: currentComment ? \"Edit comment\" : \"Add comment\",\n                handleCreate: handleSaveComment,\n                handleDestructiveAction: currentComment ? handleDeleteComment : undefined,\n                showDestructiveAction: !!currentComment,\n                actionText: isSaving ? \"Saving...\" : \"Save\",\n                destructiveActionText: \"Delete\",\n                destructiveLoading: isSaving,\n                cancelText: \"Cancel\",\n                loading: isSaving,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-destructive mb-2 text-sm\",\n                            children: [\n                                \"Error \",\n                                currentComment ? \"updating\" : \"saving\",\n                                \" \",\n                                \"comment. Please try again.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                            id: \"forecast-comment\",\n                            disabled: isSaving,\n                            rows: 4,\n                            placeholder: \"Comment\",\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"max-h-[60svh]\", {\n                                \"border-destructive\": hasError\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 21\n                        }, undefined),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Saving...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 305,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 182,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecastList, \"nS+UCuTyYJOcUReAN1tB0QJY26c=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = WeatherForecastList;\nconst WeatherForecastListSkeleton = ()=>{\n    const numRows = 3 // number of rows to render\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        headings: [\n            \"Time:firstHead\",\n            \"Location\"\n        ],\n        children: Array.from({\n            length: numRows\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"group border-b  hover: \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4 text-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 355,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 353,\n        columnNumber: 9\n    }, undefined);\n};\n_c1 = WeatherForecastListSkeleton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecastList);\nvar _c, _c1;\n$RefreshReg$(_c, \"WeatherForecastList\");\n$RefreshReg$(_c1, \"WeatherForecastListSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\n"));

/***/ })

});