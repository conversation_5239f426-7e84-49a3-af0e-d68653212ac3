"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/weather/forecast-list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square-text.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MessageSquare,MessageSquareText!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecastList = (param)=>{\n    let { logBookEntryID, refreshList = false, onClick, offline = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(true);\n    const [forecasts, setForecasts] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]);\n    const [openCommentDialog, setOpenCommentDialog] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [currentForecast, setCurrentForecast] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const [readWeatherForecasts, { loading: readWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.ReadWeatherForecasts, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readWeatherForecasts;\n            const forecasts = response === null || response === void 0 ? void 0 : (_response_readWeatherForecasts = response.readWeatherForecasts) === null || _response_readWeatherForecasts === void 0 ? void 0 : _response_readWeatherForecasts.nodes;\n            setForecasts(forecasts);\n        },\n        onError: (error)=>{\n            console.error(\"ReadWeatherForecasts error\", error);\n        }\n    });\n    const [updateWeatherForecast] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setOpenCommentDialog(false);\n            setIsSaving(false);\n            setHasError(false);\n            loadForecasts() // Refresh the list\n            ;\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast error\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    });\n    const loadForecasts = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(async ()=>{\n        if (offline) {\n            const forecasts = await forecastModel.getByLogBookEntryID(logBookEntryID);\n            setForecasts(forecasts);\n        } else {\n            await readWeatherForecasts({\n                variables: {\n                    filter: {\n                        logBookEntryID: {\n                            eq: logBookEntryID\n                        }\n                    }\n                }\n            });\n        }\n    }, [\n        offline,\n        logBookEntryID,\n        forecastModel,\n        readWeatherForecasts\n    ]);\n    const handleOpenCommentDialog = (forecast)=>{\n        setCurrentForecast(forecast);\n        setCurrentComment(forecast.comment || \"\");\n        setOpenCommentDialog(true);\n        setHasError(false);\n    };\n    const handleSaveComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: currentComment\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: currentComment\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const handleDeleteComment = async ()=>{\n        if (!currentForecast) return;\n        setIsSaving(true);\n        setHasError(false);\n        try {\n            if (offline) {\n                // Update using offline model\n                const updatedForecast = {\n                    ...currentForecast,\n                    comment: \"\"\n                };\n                // Remove GraphQL typename if present\n                if (updatedForecast.__typename) delete updatedForecast.__typename;\n                if (updatedForecast.geoLocation) delete updatedForecast.geoLocation;\n                await forecastModel.save(updatedForecast);\n                setOpenCommentDialog(false);\n                setIsSaving(false);\n                loadForecasts() // Refresh the list\n                ;\n            } else {\n                // Update using GraphQL mutation\n                const input = {\n                    id: currentForecast.id,\n                    comment: \"\"\n                };\n                await updateWeatherForecast({\n                    variables: {\n                        input\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error deleting comment:\", error);\n            setHasError(true);\n            setIsSaving(false);\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const formatDay = (day)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()(day).format(\"YYYY-MM-DD\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isLoading || refreshList) {\n            loadForecasts();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        refreshList,\n        loadForecasts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WeatherForecastListSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 183,\n                columnNumber: 45\n            }, undefined),\n            !readWeatherForecastsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: forecasts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        headings: [\n                            \"Forecast\",\n                            \"Wind\",\n                            \"Cloud\",\n                            \"Swell\",\n                            \"Comment\"\n                        ],\n                        children: forecasts.map((forecast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"group border-b  hover: \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"cursor-pointer\",\n                                            onClick: ()=>{\n                                                onClick(forecast);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-light\",\n                                                        children: dayjs__WEBPACK_IMPORTED_MODULE_8___default()(\"\".concat(forecast.day, \" \").concat(forecast.time)).format(\"DD MMMM, HH:mm\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"uppercase text-light-blue-vivid-300\",\n                                                        children: +forecast.geoLocationID > 0 ? forecast.geoLocation.title : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: forecast.windDirection,\n                                            speed: forecast.windSpeed,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            visibilityValue: forecast.visibility,\n                                            precipitationValue: forecast.precipitation,\n                                            cloudCoverValue: forecast.cloudCover,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            value: forecast.swell,\n                                            editMode: false,\n                                            iconOnly: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            iconOnly: true,\n                                            title: forecast.comment ? \"Edit comment\" : \"Add comment\",\n                                            className: \"group\",\n                                            iconLeft: forecast.comment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-curious-blue-400\"),\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 57\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"text-neutral-400 group-hover:text-neutral-400/50\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\"),\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 57\n                                            }, void 0),\n                                            onClick: ()=>handleOpenCommentDialog(forecast)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 41\n                                    }, undefined)\n                                ]\n                            }, forecast.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 37\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 185,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_7__.AlertDialogNew, {\n                openDialog: openCommentDialog,\n                setOpenDialog: setOpenCommentDialog,\n                title: currentComment ? \"Edit comment\" : \"Add comment\",\n                handleCreate: handleSaveComment,\n                handleDestructiveAction: currentComment ? handleDeleteComment : undefined,\n                showDestructiveAction: !!currentComment,\n                actionText: isSaving ? \"Saving...\" : \"Save\",\n                destructiveActionText: \"Delete\",\n                destructiveLoading: isSaving,\n                cancelText: \"Cancel\",\n                size: \"\",\n                loading: isSaving,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-destructive mb-2 text-sm\",\n                            children: [\n                                \"Error \",\n                                currentComment ? \"updating\" : \"saving\",\n                                \" \",\n                                \"comment. Please try again.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                            id: \"forecast-comment\",\n                            disabled: isSaving,\n                            rows: 4,\n                            placeholder: \"Comment\",\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"max-h-[60svh]\", {\n                                \"border-destructive\": hasError\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 21\n                        }, undefined),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MessageSquare_MessageSquareText_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Saving...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 305,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 182,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecastList, \"2xbn5GwkfoDvXkmLYY/Eialx23A=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = WeatherForecastList;\nconst WeatherForecastListSkeleton = ()=>{\n    const numRows = 3 // number of rows to render\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        headings: [\n            \"Time:firstHead\",\n            \"Location\"\n        ],\n        children: Array.from({\n            length: numRows\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"group border-b  hover: \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4 text-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n                lineNumber: 356,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast-list.tsx\",\n        lineNumber: 354,\n        columnNumber: 9\n    }, undefined);\n};\n_c1 = WeatherForecastListSkeleton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecastList);\nvar _c, _c1;\n$RefreshReg$(_c, \"WeatherForecastList\");\n$RefreshReg$(_c1, \"WeatherForecastListSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\n"));

/***/ })

});